name: Sync with Canvas Ruby v2.7

on:
  push:
    branches: [master, main]
    paths:
      - 'README.md'

jobs:
  sync:
    name: Sync with Canvas

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 2.7

      - name: Install github-to-canvas
        run: gem install github-to-canvas

      # Secret stored in learn-co-curriculum Settings/Secrets
      - name: Sync from .canvas file
        run: github-to-canvas -a -lr
        env:
          CANVAS_API_KEY: ${{ secrets.CANVAS_API_KEY }}
          CANVAS_API_PATH: ${{ secrets.CANVAS_API_PATH }}
